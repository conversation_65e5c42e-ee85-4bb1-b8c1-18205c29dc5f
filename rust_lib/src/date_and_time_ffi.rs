#![allow(non_camel_case_types)]

use std::ffi::CStr;
use std::os::raw::c_char;
use std::ptr;

use esp_idf_sys::{esp_err_t, ESP_OK, ESP_ERR_INVALID_ARG, ESP_ERR_NOT_FOUND};

use crate::date_and_time::{parse_and_store_http_date, get_date, current_datetime_string};



#[repr(C)]
#[derive(Debug, <PERSON><PERSON>, <PERSON>lone)]
pub enum week_day_t {
    SUN = 0,
    MON = 1,
    TUE = 2,
    WEN = 3,
    THU = 4,
    FRI = 5,
    SAT = 6,
}



#[repr(C)]
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub enum month_t {
    JAN = 0,
    FEB = 1,
    MAR = 2,
    APR = 3,
    MAY = 4,
    JUN = 5,
    JUL = 6,
    AUG = 7,
    SEP = 8,
    OCT = 9,
    NOV = 10,
    DEC = 11,
}



#[repr(C)]
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct date_and_time_t {
    pub week_day: week_day_t,
    pub day_n: u8,
    pub month: month_t,
    pub year: u16,
    pub hour: u8,
    pub min: u8,
    pub sec: u8,
}



static mut LAST_DATE_TIME: Option<date_and_time_t> = None;



#[no_mangle]
pub extern "C" fn set_time_from_header(data: *const c_char) -> esp_err_t {
    if data.is_null() {
        return ESP_ERR_INVALID_ARG;
    }
    let cstr = unsafe { CStr::from_ptr(data) };
    let Ok(date_str) = cstr.to_str() else { return ESP_ERR_INVALID_ARG; };

    match parse_and_store_http_date(date_str) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}



#[no_mangle]
pub extern "C" fn get_date_time() -> *const date_and_time_t {
    if let Some(dt) = get_date() {
        let date = dt.date();
        let time = dt.time();

        let weekday = match date.weekday().number_days_from_sunday() {
            0 => week_day_t::SUN,
            1 => week_day_t::MON,
            2 => week_day_t::TUE,
            3 => week_day_t::WEN,
            4 => week_day_t::THU,
            5 => week_day_t::FRI,
            _ => week_day_t::SAT,
        };

        let month = match date.month() as u8 {
            1 => month_t::JAN,
            2 => month_t::FEB,
            3 => month_t::MAR,
            4 => month_t::APR,
            5 => month_t::MAY,
            6 => month_t::JUN,
            7 => month_t::JUL,
            8 => month_t::AUG,
            9 => month_t::SEP,
            10 => month_t::OCT,
            11 => month_t::NOV,
            _ => month_t::DEC,
        };

        let c_date = date_and_time_t {
            week_day: weekday,
            day_n: date.day(),
            month,
            year: date.year() as u16,
            hour: time.hour(),
            min: time.minute(),
            sec: time.second(),
        };

        unsafe {
            LAST_DATE_TIME = Some(c_date);
            return LAST_DATE_TIME.as_ref().unwrap() as *const date_and_time_t;
        }
    }

    core::ptr::null()
}



#[no_mangle]
pub extern "C" fn write_date_time (where_ptr: *mut date_and_time_t) {
    if where_ptr.is_null() {
        return;
    }

    let date = get_date_time();

    unsafe {
        ptr::write(where_ptr, date);
    }
}



// void substract_seconds(date_and_time_t * where, uint64_t seconds);
// #[no_mangle]
// pub extern "C" fn substract_seconds(where_ptr: *mut c_char, seconds: u64){
//     if where_ptr.is_null() {
//         return;
//     }

//       TODO
// }



#[no_mangle]
pub extern "C" fn date_time_to_string(where_ptr: *mut c_char) {
    if where_ptr.is_null() {
        return;
    }

    let datetime_str = current_datetime_string();

    unsafe {
        let bytes = datetime_str.as_bytes();
        ptr::copy_nonoverlapping(bytes.as_ptr(), where_ptr as *mut u8, bytes.len());
        *where_ptr.add(bytes.len()) = 0;
    }
}

